// Navigation and dropdown interactions (moved from inline script)
(function() {
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');

    if (!mobileMenu || !menuIcon || !closeIcon) return;

    if (mobileMenu.classList.contains('hidden')) {
      mobileMenu.classList.remove('hidden');
      menuIcon.classList.add('hidden');
      closeIcon.classList.remove('hidden');
    } else {
      mobileMenu.classList.add('hidden');
      menuIcon.classList.remove('hidden');
      closeIcon.classList.add('hidden');
      // Close mobile dropdown when closing main menu
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  }

  function toggleMobileDropdown() {
    const dropdown = document.getElementById('mobile-dropdown-content');
    const arrow = document.getElementById('mobile-dropdown-arrow');
    const button = arrow ? arrow.parentElement : null;

    if (!dropdown || !arrow || !button) return;

    if (dropdown.classList.contains('expanded')) {
      dropdown.classList.remove('expanded');
      arrow.style.transform = 'rotate(0deg)';
      button.setAttribute('aria-expanded', 'false');
    } else {
      dropdown.classList.add('expanded');
      arrow.style.transform = 'rotate(180deg)';
      button.setAttribute('aria-expanded', 'true');
    }
  }

  // expose handlers for HTML onclick attributes
  window.toggleMobileMenu = toggleMobileMenu;
  window.toggleMobileDropdown = toggleMobileDropdown;

  // Close mobile menu when window is resized to desktop
  window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) {
      const mobileMenu = document.getElementById('mobile-menu');
      const menuIcon = document.getElementById('menu-icon');
      const closeIcon = document.getElementById('close-icon');
      if (mobileMenu) mobileMenu.classList.add('hidden');
      if (menuIcon) menuIcon.classList.remove('hidden');
      if (closeIcon) closeIcon.classList.add('hidden');

      // Reset mobile dropdown
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  });

  // Keyboard navigation for dropdown
  document.addEventListener('keydown', function(e) {
    if (e.key !== 'Escape') return;
    const dropdown = document.querySelector('.dropdown');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    if (!dropdown || !dropdownMenu) return;
    dropdownMenu.classList.remove('show');
    const trigger = dropdown.querySelector('button, a');
    if (trigger) trigger.setAttribute('aria-expanded', 'false');
  });

  // Toggle desktop services dropdown on focus/blur and click
  const servicesTrigger = document.querySelector('.dropdown > button');
  const servicesMenu = document.getElementById('services-menu');

  if (servicesTrigger && servicesMenu) {
    servicesTrigger.addEventListener('click', function() {
      const isOpen = servicesMenu.classList.contains('show');
      servicesMenu.classList.toggle('show', !isOpen);
      servicesTrigger.setAttribute('aria-expanded', String(!isOpen));
    });

    servicesTrigger.addEventListener('focus', function() {
      servicesTrigger.setAttribute('aria-expanded', 'true');
      servicesMenu.classList.add('show');
    });

    servicesTrigger.addEventListener('blur', function() {
      setTimeout(() => {
        if (!servicesMenu.contains(document.activeElement)) {
          servicesTrigger.setAttribute('aria-expanded', 'false');
          servicesMenu.classList.remove('show');
        }
      }, 100);
    });

    servicesMenu.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('blur', function() {
        setTimeout(() => {
          if (!servicesMenu.contains(document.activeElement)) {
            servicesTrigger.setAttribute('aria-expanded', 'false');
            servicesMenu.classList.remove('show');
          }
        }, 100);
      });
    });
  }
})();

// Video Modal Functions
(function() {
  function playVideo() {
    const modal = document.getElementById('videoModal');
    const videoFrame = document.getElementById('videoFrame');

    if (!modal || !videoFrame) return;

    // Replace with actual video URL - for now using a placeholder
    // You can replace this with your actual YouTube, Vimeo, or direct video URL
    const videoUrl = 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0';

    videoFrame.src = videoUrl;
    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  }

  function closeVideo() {
    const modal = document.getElementById('videoModal');
    const videoFrame = document.getElementById('videoFrame');

    if (!modal || !videoFrame) return;

    modal.classList.add('hidden');
    modal.classList.remove('flex');
    videoFrame.src = ''; // Stop video playback

    // Restore body scroll
    document.body.style.overflow = '';
  }

  // Close modal when clicking outside the video
  document.addEventListener('click', function(e) {
    const modal = document.getElementById('videoModal');
    if (e.target === modal) {
      closeVideo();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeVideo();
    }
  });

  // Expose functions globally
  window.playVideo = playVideo;
  window.closeVideo = closeVideo;
})();

/* ─────── 1. DATA  ─────────────────────────────────────────────────── */
const gallery = [
  {
    src: "NIRMANAH PROJECTS/revised view1.jpg",
    title: "Modern Residential Design",
    category: "Design & Consultancy",
    location: "Kochi, Kerala",
    year: "2024",
    area: "3 200 sq ft",
    desc: "Comprehensive architectural and interior design solution balancing aesthetics with functionality.",
    features: [
      "Open-plan living & dining",
      "Double-height atrium",
      "Passive cooling strategies"
    ],
    services: "Concept design, structural detailing, interior fit-out supervision"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-05-16-21-47-54.jpg",
    title: "Contemporary Home Renovation",
    category: "Renovation & Remodeling",
    location: "Kottayam, Kerala",
    year: "2023",
    area: "2 500 sq ft",
    desc: "Complete home transformation enhancing style, comfort, and usability for modern living.",
    features: [
      "New skylight above stairwell",
      "Modular kitchen upgrade",
      "Energy-efficient lighting overhaul"
    ],
    services: "Structural retrofit, interior redesign, project management"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-06-06-13-52-28.jpg",
    title: "Outdoor Living Space",
    category: "Landscaping & Outdoor Works",
    location: "Calicut, Kerala",
    year: "2023",
    area: "1 800 sq ft",
    desc: "Custom outdoor design featuring landscaping elements that blend beauty with function.",
    features: [
      "Pergola-covered sit-out",
      "Native plant palette",
      "Water feature with LED lighting"
    ],
    services: "Landscape architecture, hardscaping, lighting design"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2025-04-08-11-46-53.jpg",
    title: "Complete Project Solution",
    category: "Turnkey Projects",
    location: "Ernakulam, Kerala",
    year: "2025",
    area: "4 800 sq ft",
    desc: "End-to-end design-to-delivery solution including construction and project management.",
    features: [
      "Integrated BIM workflow",
      "Solar rooftop installation",
      "Smart-home automation"
    ],
    services: "Architecture, interior, MEP coordination, construction"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-05-16-22-02-55.jpg",
    title: "Modern Interior Design",
    category: "Design & Consultancy",
    location: "Thrissur, Kerala",
    year: "2023",
    area: "1 400 sq ft",
    desc: "Sophisticated interior design combining contemporary aesthetics with practical functionality.",
    features: [
      "Custom furniture design",
      "Hidden storage solutions",
      "Warm indirect lighting"
    ],
    services: "Interior design, FF&E procurement, site supervision"
  },
  {
    src: "NIRMANAH PROJECTS/v1 (1).jpg",
    title: "Architectural Excellence",
    category: "Design & Consultancy",
    location: "Alappuzha, Kerala",
    year: "2024",
    area: "5 600 sq ft",
    desc: "Innovative architectural design showcasing structural integrity and aesthetic appeal.",
    features: [
      "Cantilevered balconies",
      "Rainwater harvesting",
      "Facade shading fins"
    ],
    services: "Concept-to-detailed design, structural consultancy"
  }
];

/* ─────── 2. STATE  ────────────────────────────────────────────────── */
let currentIndex = 0;

/* ─────── 3. OPEN / CLOSE  ─────────────────────────────────────────── */
function openGallery(index) {
  currentIndex = index;
  createThumbnailStrip(); // Create thumbnails when opening gallery
  fillModal();
  initializeImageZoom(); // Initialize zoom functionality

  const modal = document.getElementById("galleryModal");
  modal.classList.remove("hidden");
  document.body.style.overflow = "hidden";

  // Focus management for accessibility
  const closeButton = modal.querySelector('button[aria-label="Close gallery"]');
  if (closeButton) {
    closeButton.focus();
  }

  // Trap focus within modal
  trapFocus(modal);
}

function closeGallery() {
  // Reset zoom state
  isZoomed = false;
  const modalImg = document.getElementById("modalImg");
  if (modalImg) {
    modalImg.classList.remove("zoomed");
  }

  document.getElementById("galleryModal").classList.add("hidden");
  document.body.style.overflow = "auto";

  // Return focus to the element that opened the gallery
  const portfolioSection = document.getElementById("portfolio");
  if (portfolioSection) {
    portfolioSection.focus();
  }
}

/* ─────── 4. NAVIGATION WITH SMOOTH TRANSITIONS  ──────────────────── */
function prevImage() {
  const modalImg = document.getElementById("modalImg");
  if (modalImg) {
    modalImg.classList.add("fade-out");
    setTimeout(() => {
      currentIndex = (currentIndex - 1 + gallery.length) % gallery.length;
      fillModal();
    }, 200);
  }
}

function nextImage() {
  const modalImg = document.getElementById("modalImg");
  if (modalImg) {
    modalImg.classList.add("fade-out");
    setTimeout(() => {
      currentIndex = (currentIndex + 1) % gallery.length;
      fillModal();
    }, 200);
  }
}

/* ─────── 5. POPULATE MODAL  ───────────────────────────────────────── */
function fillModal() {
  const item = gallery[currentIndex];
  const modalImg = document.getElementById("modalImg");
  const imageContainer = document.querySelector(".gallery-image-container");

  // Add loading state
  if (imageContainer) {
    imageContainer.classList.add("loading");
  }
  if (modalImg) {
    modalImg.classList.add("loading");
  }

  // Create new image to preload
  const newImg = new Image();
  newImg.onload = function() {
    // Remove loading state and update image
    if (imageContainer) {
      imageContainer.classList.remove("loading");
    }
    if (modalImg) {
      modalImg.src = item.src;
      modalImg.alt = item.title;
      modalImg.classList.remove("loading", "fade-out");
      modalImg.classList.add("fade-in");

      // Remove fade-in class after animation
      setTimeout(() => {
        modalImg.classList.remove("fade-in");
      }, 400);
    }
  };

  newImg.onerror = function() {
    // Handle error - remove loading state and show placeholder or error message
    if (imageContainer) {
      imageContainer.classList.remove("loading");
    }
    if (modalImg) {
      modalImg.classList.remove("loading");
      // You could set a placeholder image here if needed
      modalImg.src = item.src; // Still try to load the original
      modalImg.alt = item.title;
    }
  };

  // Start loading the image
  newImg.src = item.src;

  // Update text content immediately
  const modalTitle = document.getElementById("modalTitle");
  const modalCategory = document.getElementById("modalCategory");
  const modalLocation = document.getElementById("modalLocation");
  const modalYear = document.getElementById("modalYear");
  const modalArea = document.getElementById("modalArea");
  const modalDesc = document.getElementById("modalDesc");
  const modalServices = document.getElementById("modalServices");

  if (modalTitle) modalTitle.textContent = item.title;
  if (modalCategory) modalCategory.textContent = item.category;
  if (modalLocation) modalLocation.textContent = item.location;
  if (modalYear) modalYear.textContent = item.year;
  if (modalArea) modalArea.textContent = item.area;
  if (modalDesc) modalDesc.textContent = item.desc;
  if (modalServices) modalServices.textContent = item.services;

  /* Features list */
  const ul = document.getElementById("modalFeatures");
  if (ul) {
    ul.innerHTML = "";
    if (item.features && Array.isArray(item.features)) {
      item.features.forEach(feature => {
        const li = document.createElement("li");
        li.className = "flex items-start space-x-2";
        li.innerHTML = `
          <svg class="w-4 h-4 mt-0.5 flex-shrink-0" style="color: #D4941E;" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span class="text-gray-600">${feature}</span>
        `;
        ul.appendChild(li);
      });
    }
  }

  // Update thumbnail strip
  updateThumbnailStrip();
}

/* ─────── 6. THUMBNAIL STRIP  ──────────────────────────────────────── */
function createThumbnailStrip() {
  const thumbnailStrip = document.getElementById("thumbnailStrip");
  if (!thumbnailStrip) return;

  thumbnailStrip.innerHTML = "";

  gallery.forEach((item, index) => {
    const thumbnail = document.createElement("img");
    thumbnail.src = item.src;
    thumbnail.alt = item.title;
    thumbnail.className = "thumbnail-item object-cover";
    thumbnail.onclick = () => jumpToImage(index);
    thumbnailStrip.appendChild(thumbnail);
  });
}

function updateThumbnailStrip() {
  const thumbnails = document.querySelectorAll(".thumbnail-item");
  thumbnails.forEach((thumb, index) => {
    thumb.classList.toggle("active", index === currentIndex);
  });
}

function jumpToImage(index) {
  const modalImg = document.getElementById("modalImg");
  if (modalImg && index !== currentIndex) {
    modalImg.classList.add("fade-out");
    setTimeout(() => {
      currentIndex = index;
      fillModal();
    }, 200);
  }
}

/* ─────── 6.5. IMAGE ZOOM FUNCTIONALITY  ───────────────────────────── */
let isZoomed = false;

function toggleImageZoom() {
  const modalImg = document.getElementById("modalImg");
  if (!modalImg) return;

  isZoomed = !isZoomed;
  modalImg.classList.toggle("zoomed", isZoomed);
}

// Add click event to image for zoom
function initializeImageZoom() {
  const modalImg = document.getElementById("modalImg");
  if (modalImg) {
    modalImg.addEventListener("click", toggleImageZoom);
  }
}

/* ─────── 7. FULL GALLERY GRID  ────────────────────────────────────── */
function openFullGallery() {
  const fullGalleryModal = document.getElementById("fullGalleryModal");
  const fullGalleryGrid = document.getElementById("fullGalleryGrid");

  if (!fullGalleryModal || !fullGalleryGrid) return;

  // Populate grid with all images
  fullGalleryGrid.innerHTML = "";
  gallery.forEach((item, index) => {
    const img = document.createElement("img");
    img.src = item.src;
    img.alt = item.title;
    img.className = "w-full aspect-square object-cover rounded-lg cursor-pointer transition-transform duration-200 hover:scale-105";
    img.onclick = () => {
      closeFullGallery();
      currentIndex = index;
      fillModal();
    };
    fullGalleryGrid.appendChild(img);
  });

  fullGalleryModal.classList.remove("hidden");
  document.body.style.overflow = "hidden";
}

function closeFullGallery() {
  const fullGalleryModal = document.getElementById("fullGalleryModal");
  if (fullGalleryModal) {
    fullGalleryModal.classList.add("hidden");
    document.body.style.overflow = "auto";
  }
}

/* ─────── 8. EVENT HELPERS  ───────────────────────────────────────── */
document.getElementById("galleryModal").addEventListener("click", e => {
  if (e.target === e.currentTarget) closeGallery();      // click outside content
});

// Full gallery modal click outside to close
document.addEventListener("DOMContentLoaded", () => {
  const fullGalleryModal = document.getElementById("fullGalleryModal");
  if (fullGalleryModal) {
    fullGalleryModal.addEventListener("click", e => {
      if (e.target === e.currentTarget) closeFullGallery();
    });
  }
});

document.addEventListener("keydown", e => {
  // Handle gallery modal keys
  const modal = document.getElementById("galleryModal");
  const isGalleryOpen = !modal.classList.contains("hidden");

  if (isGalleryOpen) {
    e.preventDefault(); // Prevent default browser behavior

    switch(e.key) {
      case "Escape":
        closeGallery();
        break;
      case "ArrowLeft":
      case "a":
      case "A":
        prevImage();
        break;
      case "ArrowRight":
      case "d":
      case "D":
        nextImage();
        break;
      case "f":
      case "F":
        openFullGallery();
        break;
      case "z":
      case "Z":
        toggleImageZoom();
        break;
      case "Home":
        jumpToImage(0);
        break;
      case "End":
        jumpToImage(gallery.length - 1);
        break;
      case " ": // Spacebar
        nextImage();
        break;
    }
    return;
  }

  // Handle full gallery modal keys
  const fullModal = document.getElementById("fullGalleryModal");
  const isFullGalleryOpen = fullModal && !fullModal.classList.contains("hidden");

  if (isFullGalleryOpen) {
    if (e.key === "Escape") {
      e.preventDefault();
      closeFullGallery();
    }
  }
});

/* ─────── 9. ACCESSIBILITY HELPERS  ───────────────────────────────── */
function trapFocus(element) {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  if (focusableElements.length === 0) return;

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  element.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }
  });
}

/* ─────── 10. EXPOSE FUNCTIONS GLOBALLY  ─────────────────────────────── */
window.openGallery = openGallery;
window.closeGallery = closeGallery;
window.prevImage = prevImage;
window.nextImage = nextImage;
window.openFullGallery = openFullGallery;
window.closeFullGallery = closeFullGallery;
window.jumpToImage = jumpToImage;

